# WhatsApp Web.js files
/.wwebjs_auth
/.wwebjs_cache

# Dependencies
/node_modules

# Environment variables
.env
.env.local
.env.production

# Build output
/dist

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo