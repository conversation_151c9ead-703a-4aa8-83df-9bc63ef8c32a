# 🤖 WhatsApp Bot AI dengan Gemini & Function Tools

Bot WhatsApp cerdas yang menggunakan Google Gemini AI dengan dukungan function tools untuk informasi waktu dan tanggal real-time. Bot ini dapat memproses pesan teks, gambar, dan memberikan respons dalam bahasa Indonesia dengan data yang akurat.

## ✨ Fitur Utama

- 🤖 **AI Gemini Integration**: Menggunakan model Gemini 2.5 Flash Lite untuk respons yang cerdas
- 🇮🇩 **Bahasa Indonesia**: <PERSON><PERSON><PERSON> respons dalam bahasa Indonesia
- ⏰ **Real-time Date & Time**: Function tools untuk informasi waktu dan tanggal akurat
- 📱 **WhatsApp Web Integration**: Terintegrasi langsung dengan WhatsApp Web
- 🖼️ **Multi-media Support**: Mendukung pesan teks dan gambar
- 💾 **Multi-User Memory**: Sistem memori per-nomor WhatsApp dengan auto-management
- 🔗 **Reference Links**: Menyertakan link referensi dalam setiap jawaban

## 🛠️ Function Tools

Bot dilengkapi dengan function tools untuk:

### 📅 getCurrentDateTime
Mendapatkan tanggal dan waktu saat ini dalam format Indonesia
- **Format**: `full`, `date`, `time`
- **Timezone**: WIB, WITA, WIT
- **Contoh**: "Jam berapa sekarang?" atau "Tanggal hari ini?"

### 📊 getDateInfo
Mendapatkan informasi detail tentang tanggal tertentu
- **Input**: Format YYYY-MM-DD atau 'today'
- **Output**: Hari, tanggal, bulan, tahun, hari ke-berapa dalam tahun, dll.
- **Contoh**: "Info tanggal 2024-12-25" atau "Info hari ini"

## 🚀 Deploy ke Railway (Recommended)

### Persiapan
1. Fork atau clone repository ini
2. Dapatkan API Key dari [Google AI Studio](https://makersuite.google.com/app/apikey)
3. Buat akun di [Railway](https://railway.app)

### Langkah Deploy
1. **Connect Repository**
   - Login ke Railway
   - Klik "New Project" → "Deploy from GitHub repo"
   - Pilih repository ini

2. **Set Environment Variables**
   ```
   API_KEY=your_gemini_api_key_here
   NODE_ENV=production
   ```

3. **Deploy**
   - Railway akan otomatis build dan deploy
   - Tunggu hingga status "Active"

### Setelah Deploy
1. Buka URL aplikasi Railway
2. Scan QR Code yang muncul dengan WhatsApp
3. Bot siap digunakan!

## 📋 Daftar Isi

- [Deploy ke Railway](#-deploy-ke-railway-recommended)
- [Prasyarat](#prasyarat)
- [Instalasi](#instalasi)
- [Konfigurasi](#konfigurasi)
- [Penggunaan](#penggunaan)
- [Perintah Bot](#perintah-bot)
- [Struktur Project](#struktur-project)
- [Troubleshooting](#troubleshooting)
- [Kontribusi](#kontribusi)

## 🔧 Prasyarat

Pastikan Anda telah menginstal:

- **Node.js** (v16.x atau lebih baru)
- **npm** (v8.x atau lebih baru)
- **Git** (opsional tapi direkomendasikan)
- **Google AI API Key** (dari Google AI Studio)

## 📦 Instalasi

1. **Clone Repository:**
   ```bash
   git clone https://github.com/vickyymosafan/whatsapp-ai.git
   cd whatsapp-ai
   ```

2. **Install Dependencies:**
   ```bash
   npm install
   ```

3. **Build Project:**
   ```bash
   npm run build
   ```

## ⚙️ Konfigurasi

1. **Buat File Environment:**
   Buat file `.env` di root directory:
   ```env
   API_KEY=your_google_generative_ai_api_key_here
   ```

2. **Dapatkan Google AI API Key:**
   - Kunjungi [Google AI Studio](https://aistudio.google.com/)
   - Buat API key baru
   - Copy dan paste ke file `.env`

## 🚀 Penggunaan

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Langkah-langkah:

1. **Jalankan Aplikasi:**
   ```bash
   npm run dev
   ```

2. **Scan QR Code:**
   - QR code akan muncul di terminal
   - Buka WhatsApp di ponsel Anda
   - Pilih "WhatsApp Web" dan scan QR code

3. **Bot Siap Digunakan:**
   - Bot akan menampilkan "Klien WhatsApp Web sudah siap!"
   - Mulai kirim pesan dengan awalan "/"

## 🎯 Perintah Bot

### Memulai Percakapan
Semua pesan harus diawali dengan `/` (slash):
```
/halo
/tanya tentang cuaca hari ini
/jam berapa sekarang?
/info tanggal hari ini
```

### Perintah Khusus
- `/selesai` - Menghapus memori percakapan secara manual
- `/status` - Melihat status bot dan informasi memory
- **Auto-clear**: Memori dihapus otomatis setiap 4 jam

### Contoh Penggunaan Function Tools
```
/jam berapa sekarang?
/tanggal hari ini lengkap
/info tanggal 2024-12-25
/hari apa tanggal 1 Januari 2025?
```

## 👥 Multi-User Support

Bot mendukung multiple user secara bersamaan dengan fitur:

### 🔐 **Privacy & Isolation**
- Setiap nomor WhatsApp memiliki chat session terpisah
- History percakapan tidak tercampur antar user
- Memory management individual per nomor

### 📊 **User Management**
- Auto-detection nomor WhatsApp baru
- Tracking aktivitas per user (message count, last activity)
- Auto-cleanup user tidak aktif untuk optimasi memory

### 🎯 **Personalized Experience**
- Bot mengingat konteks percakapan per user
- Setiap user dapat mengontrol memory sendiri dengan `/selesai`
- Status individual tersedia melalui `/status`

## 🧠 Memory Management Per-Nomor

Bot dilengkapi dengan sistem memory management yang canggih dengan dukungan multi-user:

### 📱 **Sistem Per-Nomor WhatsApp**
- **Isolated Memory**: Setiap nomor WhatsApp memiliki chat session terpisah
- **User Tracking**: Bot mengenali dan mengingat percakapan per nomor
- **Cross-User Privacy**: Percakapan antar nomor tidak tercampur

### 🔄 **Auto-Clear Memory**
- **Global Clear**: Setiap 4 jam semua memory dihapus
- **Inactive Cleanup**: User tidak aktif >2 jam dihapus otomatis setiap 30 menit
- **Smart Management**: Optimasi memory berdasarkan aktivitas user

### 📊 **Manual Controls**
- **`/selesai`**: Hapus memory nomor Anda saja
- **`/status`**: Lihat status memory Anda dan statistik global
- **Individual Control**: Setiap user mengontrol memory sendiri

### ⚡ **Advanced Features**
- **Message Counter**: Tracking jumlah pesan per user
- **Activity Tracking**: Monitoring waktu aktivitas terakhir
- **Global Statistics**: Admin bisa melihat total user aktif
- **Backup Check**: Pengecekan tambahan untuk stabilitas

### 📈 **Statistik yang Tersedia**
- Nomor WhatsApp yang digunakan
- Status memory (tersimpan/kosong)
- Jumlah pesan yang dikirim
- Total user aktif di sistem
- Waktu clear global terakhir dan berikutnya

## 📁 Struktur Project

```
whatsapp-ai/
├── src/
│   └── index.ts          # Main application file
├── dist/                 # Compiled JavaScript files
├── node_modules/         # Dependencies
├── .env                  # Environment variables
├── package.json          # Project configuration
├── tsconfig.json         # TypeScript configuration
└── README.md            # Documentation
```

## 🔍 Troubleshooting

### Error: QR Code tidak muncul
- Pastikan tidak ada instance WhatsApp Web lain yang aktif
- Restart aplikasi dengan `npm run dev`

### Error: API Key tidak valid
- Periksa file `.env` dan pastikan API key benar
- Pastikan API key memiliki akses ke Gemini API

### Error: Puppeteer tidak bisa jalan
- Jika running sebagai root, tambahkan flag `--no-sandbox`
- Install dependencies yang diperlukan untuk Chromium

### Bot tidak merespons
- Pastikan pesan diawali dengan `/`
- Cek console untuk error messages
- Pastikan koneksi internet stabil

## 🤝 Kontribusi

Kontribusi sangat diterima! Silakan:

1. Fork repository ini
2. Buat branch fitur baru (`git checkout -b feature/AmazingFeature`)
3. Commit perubahan (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 Lisensi

Project ini menggunakan lisensi ISC. Lihat file `LICENSE` untuk detail lebih lanjut.

## 🙏 Acknowledgments

- [WhatsApp Web.js](https://github.com/pedroslopez/whatsapp-web.js) - WhatsApp Web API
- [Google Generative AI](https://ai.google.dev/) - Gemini AI API
- [Express.js](https://expressjs.com/) - Web framework
- [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript

## 📞 Support

Jika Anda mengalami masalah atau memiliki pertanyaan:

- 🐛 **Bug Reports**: Buat issue di GitHub
- 💡 **Feature Requests**: Diskusikan di GitHub Discussions
- 📧 **Contact**: Hubungi maintainer melalui GitHub

## 🔄 Changelog

### v2.0.0 (Latest) - Multi-User Memory System
- 🚀 **BREAKING**: Sistem memory per-nomor WhatsApp (isolated sessions)
- 📱 **NEW**: Setiap nomor memiliki chat session dan history terpisah
- 👥 **NEW**: Multi-user support dengan privacy terjamin
- 📊 **NEW**: Statistik per-user dan global dalam `/status`
- 🗑️ **NEW**: Auto-cleanup user tidak aktif (>2 jam) setiap 30 menit
- ⚡ **IMPROVED**: Memory management yang lebih efisien
- 🔧 **IMPROVED**: Logging dengan identifikasi nomor user

### v1.2.0
- ✅ Auto-clear memory setiap 4 jam untuk performa optimal
- ✅ Perintah `/status` untuk monitoring bot
- ✅ Peningkatan pesan welcome dan instruksi
- ✅ Backup check untuk memory management

### v1.1.0
- ✅ Tambah function tools untuk date dan time real-time
- ✅ Integrasi dengan Gemini 2.5 Flash Lite
- ✅ Dukungan timezone Indonesia (WIB, WITA, WIT)
- ✅ Peningkatan dokumentasi

### v1.0.0
- ✅ WhatsApp Bot dasar dengan Gemini AI
- ✅ Dukungan pesan teks dan gambar
- ✅ Sistem memori percakapan
- ✅ Respons dalam bahasa Indonesia

---

**Dibuat dengan ❤️ untuk komunitas developer Indonesia**

