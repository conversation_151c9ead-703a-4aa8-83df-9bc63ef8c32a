<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Bot AI - Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qr-section {
            text-align: center;
        }

        .qr-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .qr-code {
            max-width: 250px;
            max-height: 250px;
            border-radius: 10px;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;
            margin: 10px 0;
        }

        .status-connecting {
            background: #fff3cd;
            color: #856404;
        }

        .status-ready {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .logs-section {
            grid-column: 1 / -1;
        }

        .logs-container {
            background: #1a1a1a;
            border-radius: 10px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-entry {
            margin-bottom: 5px;
            color: #00ff00;
        }

        .log-timestamp {
            color: #888;
        }

        .log-info {
            color: #00bfff;
        }

        .log-error {
            color: #ff4444;
        }

        .log-warning {
            color: #ffaa00;
        }

        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s ease;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fab fa-whatsapp"></i> WhatsApp Bot AI Dashboard</h1>
            <p>Kelola dan monitor bot WhatsApp AI Anda dengan mudah</p>
        </div>

        <div class="dashboard">
            <!-- QR Code Section -->
            <div class="card qr-section">
                <h2><i class="fas fa-qrcode"></i> QR Code Scanner</h2>
                <div class="qr-container" id="qrContainer">
                    <div class="loading"></div>
                    <p>Memuat QR Code...</p>
                </div>
                <div class="status-indicator status-connecting" id="connectionStatus">
                    <i class="fas fa-circle"></i>
                    <span>Menghubungkan...</span>
                </div>
                <button class="refresh-btn" onclick="refreshQR()">
                    <i class="fas fa-refresh"></i> Refresh QR
                </button>
            </div>

            <!-- Statistics Section -->
            <div class="card">
                <h2><i class="fas fa-chart-bar"></i> Statistik Bot</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="activeUsers">0</div>
                        <div class="stat-label">User Aktif</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalMessages">0</div>
                        <div class="stat-label">Total Pesan</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="uptime">0h 0m</div>
                        <div class="stat-label">Uptime</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="lastClear">-</div>
                        <div class="stat-label">Clear Terakhir</div>
                    </div>
                </div>
            </div>

            <!-- Logs Section -->
            <div class="card logs-section">
                <h2><i class="fas fa-terminal"></i> Activity Logs</h2>
                <div class="logs-container" id="logsContainer">
                    <div class="log-entry">
                        <span class="log-timestamp">[Loading...]</span>
                        <span class="log-info">Memuat logs...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/dashboard.js"></script>
</body>
</html>
