import express from "express";
import { Client, LocalAuth, Message, MessageMedia } from "whatsapp-web.js";
import qrcode from "qrcode-terminal";
import QRCode from "qrcode";
import { GoogleGenerativeAI, ChatSession } from "@google/generative-ai";
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import path from 'path';
import 'dotenv/config';

const genAI = new GoogleGenerativeAI(process.env.API_KEY!);

// Function tools untuk date dan time
const dateTimeFunctions = {
  getCurrentDateTime: {
    name: "getCurrentDateTime",
    description: "Mendapatkan tanggal dan waktu saat ini dalam format Indonesia (WIB)",
    parameters: {
      type: "object",
      properties: {
        format: {
          type: "string",
          description: "Format output: 'full' untuk lengkap, 'date' untuk tanggal saja, 'time' untuk waktu saja",
          enum: ["full", "date", "time"]
        },
        timezone: {
          type: "string",
          description: "Zona waktu Indonesia",
          enum: ["WIB", "WITA", "WIT"]
        }
      },
      required: ["format"]
    }
  },

  getDateInfo: {
    name: "getDateInfo",
    description: "Mendapatkan informasi detail tentang tanggal tertentu",
    parameters: {
      type: "object",
      properties: {
        date: {
          type: "string",
          description: "Tanggal dalam format YYYY-MM-DD atau 'today' untuk hari ini"
        }
      },
      required: ["date"]
    }
  }
};

// Function untuk menjalankan tools
function executeFunction(functionName: string, args: any) {
  switch (functionName) {
    case "getCurrentDateTime":
      return getCurrentDateTime(args.format, args.timezone || "WIB");

    case "getDateInfo":
      return getDateInfo(args.date);

    default:
      return { error: "Function tidak ditemukan" };
  }
}

// Implementasi function getCurrentDateTime
function getCurrentDateTime(format: string, timezone: string = "WIB") {
  const now = new Date();

  // Offset untuk zona waktu Indonesia
  const timezoneOffsets = {
    "WIB": 7,  // UTC+7
    "WITA": 8, // UTC+8
    "WIT": 9   // UTC+9
  };

  const offset = timezoneOffsets[timezone as keyof typeof timezoneOffsets] || 7;
  const localTime = new Date(now.getTime() + (offset * 60 * 60 * 1000));

  const days = ["Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"];
  const months = [
    "Januari", "Februari", "Maret", "April", "Mei", "Juni",
    "Juli", "Agustus", "September", "Oktober", "November", "Desember"
  ];

  const dayName = days[localTime.getUTCDay()];
  const day = localTime.getUTCDate();
  const month = months[localTime.getUTCMonth()];
  const year = localTime.getUTCFullYear();
  const hours = localTime.getUTCHours().toString().padStart(2, '0');
  const minutes = localTime.getUTCMinutes().toString().padStart(2, '0');
  const seconds = localTime.getUTCSeconds().toString().padStart(2, '0');

  switch (format) {
    case "full":
      return {
        datetime: `${dayName}, ${day} ${month} ${year} pukul ${hours}:${minutes}:${seconds} ${timezone}`,
        timestamp: localTime.toISOString(),
        timezone: timezone
      };

    case "date":
      return {
        date: `${dayName}, ${day} ${month} ${year}`,
        timestamp: localTime.toISOString(),
        timezone: timezone
      };

    case "time":
      return {
        time: `${hours}:${minutes}:${seconds} ${timezone}`,
        timestamp: localTime.toISOString(),
        timezone: timezone
      };

    default:
      return {
        datetime: `${dayName}, ${day} ${month} ${year} pukul ${hours}:${minutes}:${seconds} ${timezone}`,
        timestamp: localTime.toISOString(),
        timezone: timezone
      };
  }
}

// Implementasi function getDateInfo
function getDateInfo(dateInput: string) {
  let targetDate: Date;

  if (dateInput.toLowerCase() === "today") {
    targetDate = new Date();
  } else {
    targetDate = new Date(dateInput);
    if (isNaN(targetDate.getTime())) {
      return { error: "Format tanggal tidak valid. Gunakan YYYY-MM-DD atau 'today'" };
    }
  }

  // Konversi ke WIB
  const wibTime = new Date(targetDate.getTime() + (7 * 60 * 60 * 1000));

  const days = ["Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"];
  const months = [
    "Januari", "Februari", "Maret", "April", "Mei", "Juni",
    "Juli", "Agustus", "September", "Oktober", "November", "Desember"
  ];

  const dayName = days[wibTime.getUTCDay()];
  const day = wibTime.getUTCDate();
  const month = months[wibTime.getUTCMonth()];
  const year = wibTime.getUTCFullYear();

  // Hitung hari dalam tahun
  const startOfYear = new Date(year, 0, 1);
  const dayOfYear = Math.floor((targetDate.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24)) + 1;

  // Hitung minggu dalam tahun
  const weekOfYear = Math.ceil(dayOfYear / 7);

  return {
    fullDate: `${dayName}, ${day} ${month} ${year}`,
    dayName: dayName,
    day: day,
    month: month,
    year: year,
    dayOfYear: dayOfYear,
    weekOfYear: weekOfYear,
    isWeekend: wibTime.getUTCDay() === 0 || wibTime.getUTCDay() === 6,
    timestamp: targetDate.toISOString()
  };
}

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));

// Railway akan set PORT otomatis, fallback ke 3000 untuk development
const port = parseInt(process.env.PORT || '3000', 10);

// Variables untuk QR code dan status
let currentQRCode: string | null = null;
let isWhatsAppReady: boolean = false;
let totalMessages: number = 0;

async function mediaToGenerativePart(media: MessageMedia) {
  return {
    inlineData: { data: media.data, mimeType: media.mimetype },
  };
}

const whatsappClient = new Client({
  authStrategy: new LocalAuth(),
  puppeteer: {
    headless: process.env.NODE_ENV === 'production' ? true : false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-blink-features=AutomationControlled',
      '--disable-extensions',
      '--disable-plugins',
      '--disable-default-apps',
      '--disable-component-extensions-with-background-pages',
      '--disable-ipc-flooding-protection'
    ],
    timeout: 60000,
    executablePath: process.env.NODE_ENV === 'production' ? undefined : undefined,
    handleSIGINT: false,
    handleSIGTERM: false,
    handleSIGHUP: false
  },
});

whatsappClient.on("qr", async (qr: string) => {
  qrcode.generate(qr, { small: true });
  console.log("Kode QR diterima, pindai dengan ponsel Anda.");

  try {
    // Generate QR code sebagai base64 untuk frontend
    const qrBase64 = await QRCode.toDataURL(qr, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    // Simpan QR code untuk frontend (hanya base64 part)
    currentQRCode = qrBase64.split(',')[1];
    isWhatsAppReady = false;

    // Kirim QR code ke semua client yang terhubung
    io.emit('qr', { type: 'qr', qr: currentQRCode });
  } catch (error) {
    console.error('Error generating QR code:', error);
  }
});

whatsappClient.on("ready", () => {
  console.log("Klien WhatsApp Web sudah siap!");
  console.log("🕐 Auto-clear memory diaktifkan: setiap 4 jam");
  console.log("🔄 Cleanup user tidak aktif: setiap 30 menit");
  console.log(`⏰ Waktu mulai: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}`);

  // Update status untuk frontend
  currentQRCode = null;
  isWhatsAppReady = true;

  // Kirim status ready ke semua client
  io.emit('ready', { type: 'ready' });
});

whatsappClient.on("disconnected", (reason: string) => {
  console.log("WhatsApp terputus:", reason);
  isWhatsAppReady = false;
  currentQRCode = null;

  // Kirim status disconnected ke semua client
  io.emit('disconnected', { type: 'disconnected', reason: reason });
});

whatsappClient.on("auth_failure", (msg: string) => {
  console.error("Autentikasi gagal:", msg);
  isWhatsAppReady = false;
  currentQRCode = null;

  // Kirim status auth failure ke semua client
  io.emit('auth_failure', { type: 'auth_failure', message: msg });
});

// Error handling untuk Puppeteer
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Jangan exit process, biarkan aplikasi tetap berjalan
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Jangan exit process untuk error Puppeteer
  if (error.message && error.message.includes('Protocol error')) {
    console.log('Puppeteer protocol error detected, continuing...');
    return;
  }
});

whatsappClient.on("message", async (msg: Message) => {
  const senderNumber: string = msg.from;
  const message: string = msg.body;

  console.log(`Pesan diterima dari ${senderNumber}: ${message}`);
  totalMessages++;

  // Kirim info pesan ke frontend
  io.emit('message', {
    type: 'message',
    from: cleanPhoneNumber(senderNumber),
    message: message.substring(0, 50) + (message.length > 50 ? '...' : '')
  });

  // Cek apakah pesan dimulai dengan "/"
  if (!message.startsWith("/")) {
    await sendWhatsAppMessage("🤖 **Halo! Selamat datang di WhatsApp Bot AI**\n\n" +
      "Untuk memulai percakapan dengan bot, silakan awali pesan Anda dengan tanda '/' (slash).\n\n" +
      "**Contoh penggunaan:**\n" +
      "• /halo\n" +
      "• /tanya tentang cuaca\n" +
      "• /jam berapa sekarang?\n\n" +
      "**Perintah khusus:**\n" +
      "• /selesai - Hapus memori percakapan\n" +
      "• /status - Lihat status bot\n\n" +
      "ℹ️ *Memori percakapan akan dihapus otomatis setiap 4 jam*", senderNumber);
    return;
  }

  // Hapus "/" dari awal pesan
  const cleanMessage = message.substring(1);

  // Cek apakah pesan adalah perintah "/selesai"
  if (cleanMessage.toLowerCase() === "selesai") {
    const deleted = clearUserMemory(senderNumber);
    if (deleted) {
      await sendWhatsAppMessage("✅ Memori percakapan Anda telah dihapus secara manual. Anda dapat memulai percakapan baru sekarang.", senderNumber);
    } else {
      await sendWhatsAppMessage("ℹ️ Memori Anda sudah kosong.", senderNumber);
    }
    return;
  }

  // Cek apakah pesan adalah perintah "/status"
  if (cleanMessage.toLowerCase() === "status") {
    const now = new Date();
    const timeSinceLastGlobalClear = now.getTime() - lastGlobalMemoryClearTime.getTime();
    const timeUntilNextGlobalClear = MEMORY_CLEAR_INTERVAL - timeSinceLastGlobalClear;

    const hoursUntilClear = Math.floor(timeUntilNextGlobalClear / (1000 * 60 * 60));
    const minutesUntilClear = Math.floor((timeUntilNextGlobalClear % (1000 * 60 * 60)) / (1000 * 60));

    const userStats = getUserStats(senderNumber);
    const globalStats = getGlobalStats();
    const cleanNumber = cleanPhoneNumber(senderNumber);

    const statusMessage = `📊 **Status Bot WhatsApp AI**\n\n` +
      `🤖 Status: Aktif dan siap melayani\n` +
      `📱 Nomor Anda: ${cleanNumber}\n` +
      `💾 Memori Anda: ${userStats ? 'Tersimpan' : 'Kosong'}\n` +
      `💬 Pesan Anda: ${userStats ? userStats.messageCount : 0}\n` +
      `� Total User Aktif: ${globalStats.totalActiveUsers}\n` +
      `🕐 Global clear terakhir: ${lastGlobalMemoryClearTime.toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}\n` +
      `⏰ Global clear berikutnya: ${hoursUntilClear}j ${minutesUntilClear}m lagi\n` +
      `🔄 Auto-clear: Setiap 4 jam (global)\n` +
      `🗑️ Cleanup tidak aktif: Setiap 30 menit\n\n` +
      `**Perintah tersedia:**\n` +
      `• /selesai - Hapus memori Anda\n` +
      `• /status - Lihat status ini\n` +
      `• /[pertanyaan] - Tanya AI`;

    await sendWhatsAppMessage(statusMessage, senderNumber);
    return;
  }

  let mediaPart = null;

  if (msg.hasMedia) {
    const media = await msg.downloadMedia();
    mediaPart = await mediaToGenerativePart(media);
  }

  await run(cleanMessage, senderNumber, mediaPart);
});

whatsappClient.initialize();

// Interface untuk menyimpan data chat per nomor
interface UserChatData {
  chat: ChatSession;
  lastActivity: Date;
  messageCount: number;
  createdAt: Date;
}

// Map untuk menyimpan chat session per nomor WhatsApp
const userChats = new Map<string, UserChatData>();
let lastGlobalMemoryClearTime: Date = new Date();

// Timer untuk menghapus memori setiap 4 jam (4 * 60 * 60 * 1000 ms)
const MEMORY_CLEAR_INTERVAL = 4 * 60 * 60 * 1000; // 4 jam dalam milliseconds
const USER_INACTIVE_TIMEOUT = 2 * 60 * 60 * 1000; // 2 jam untuk user tidak aktif

// Fungsi untuk membersihkan nomor WhatsApp (menghilangkan @c.us, dll)
function cleanPhoneNumber(phoneNumber: string): string {
  return phoneNumber.replace('@c.us', '').replace('@g.us', '');
}

// Fungsi untuk mendapatkan atau membuat chat session untuk nomor tertentu
function getUserChat(phoneNumber: string): ChatSession {
  const cleanNumber = cleanPhoneNumber(phoneNumber);

  if (userChats.has(cleanNumber)) {
    const userData = userChats.get(cleanNumber)!;
    userData.lastActivity = new Date();
    userData.messageCount++;
    return userData.chat;
  }

  // Buat chat session baru untuk nomor ini
  const model = genAI.getGenerativeModel({
    model: "gemini-2.5-pro-preview-06-05"
  });

  const newChat = model.startChat({
    generationConfig: {
      maxOutputTokens: 65536,
    },
    history: []
  });

  const userData: UserChatData = {
    chat: newChat,
    lastActivity: new Date(),
    messageCount: 1,
    createdAt: new Date()
  };

  userChats.set(cleanNumber, userData);
  console.log(`📱 Chat session baru dibuat untuk nomor: ${cleanNumber}`);

  return newChat;
}

// Fungsi untuk menghapus memori nomor tertentu
function clearUserMemory(phoneNumber: string): boolean {
  const cleanNumber = cleanPhoneNumber(phoneNumber);
  const deleted = userChats.delete(cleanNumber);
  if (deleted) {
    console.log(`🧹 Memori dihapus untuk nomor: ${cleanNumber} pada ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}`);
  }
  return deleted;
}

// Fungsi untuk menghapus semua memori
function clearAllMemory(): void {
  const totalUsers = userChats.size;
  userChats.clear();
  lastGlobalMemoryClearTime = new Date();
  console.log(`🧹 Semua memori dihapus (${totalUsers} users) pada: ${lastGlobalMemoryClearTime.toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}`);
}

// Fungsi untuk membersihkan user yang tidak aktif
function cleanupInactiveUsers(): void {
  const now = new Date();
  let cleanedCount = 0;

  for (const [phoneNumber, userData] of userChats.entries()) {
    const timeSinceLastActivity = now.getTime() - userData.lastActivity.getTime();
    if (timeSinceLastActivity > USER_INACTIVE_TIMEOUT) {
      userChats.delete(phoneNumber);
      cleanedCount++;
      console.log(`🗑️ User tidak aktif dihapus: ${phoneNumber}`);
    }
  }

  if (cleanedCount > 0) {
    console.log(`🧹 Cleanup selesai: ${cleanedCount} user tidak aktif dihapus`);
  }
}

// Set interval untuk auto-clear memory global
setInterval(() => {
  clearAllMemory();
}, MEMORY_CLEAR_INTERVAL);

// Set interval untuk cleanup user tidak aktif (setiap 30 menit)
setInterval(() => {
  cleanupInactiveUsers();
}, 30 * 60 * 1000);

// Fungsi untuk cek apakah perlu clear memory global (backup check)
function checkGlobalMemoryClearNeeded(): boolean {
  const now = new Date();
  const timeDiff = now.getTime() - lastGlobalMemoryClearTime.getTime();
  return timeDiff >= MEMORY_CLEAR_INTERVAL;
}

// Fungsi untuk mendapatkan statistik user
function getUserStats(phoneNumber: string): UserChatData | null {
  const cleanNumber = cleanPhoneNumber(phoneNumber);
  return userChats.get(cleanNumber) || null;
}

// Fungsi untuk mendapatkan statistik global
function getGlobalStats() {
  return {
    totalActiveUsers: userChats.size,
    lastGlobalClear: lastGlobalMemoryClearTime,
    userList: Array.from(userChats.entries()).map(([phone, data]) => ({
      phone,
      messageCount: data.messageCount,
      lastActivity: data.lastActivity,
      createdAt: data.createdAt
    }))
  };
}

async function run(message: string, senderNumber: string, mediaPart?: any): Promise<void> {
  try {
    // Cek apakah perlu clear memory global (backup check)
    if (checkGlobalMemoryClearNeeded()) {
      clearAllMemory();
    }

    // Dapatkan atau buat chat session untuk nomor ini
    const chat = getUserChat(senderNumber);
    const cleanNumber = cleanPhoneNumber(senderNumber);

    let prompt: any[] = [];

    // Tambahkan instruksi bahasa Indonesia, data-driven, dan link referensi ke setiap pesan
    const indonesianInstruction = `Jawab dalam bahasa Indonesia dengan data dan fakta yang akurat, serta WAJIB sertakan link referensi di akhir jawaban.

Kamu memiliki akses ke function tools untuk mendapatkan informasi waktu dan tanggal real-time:
- getCurrentDateTime: untuk mendapatkan tanggal dan waktu saat ini
- getDateInfo: untuk mendapatkan informasi detail tentang tanggal tertentu

Gunakan tools ini ketika user menanyakan tentang waktu, tanggal, atau informasi terkait waktu.

Pertanyaan: ${message}`;
    prompt.push(indonesianInstruction);

    if (mediaPart) {
      prompt.push(mediaPart);
    }

    const result = await chat.sendMessage(prompt);
    const response = result.response;

    // Cek apakah ada function calls
    const functionCalls = response.functionCalls();

    if (functionCalls && functionCalls.length > 0) {
      // Eksekusi function calls
      const functionResponses = functionCalls.map(call => {
        const functionResult = executeFunction(call.name, call.args);
        return {
          name: call.name,
          response: functionResult
        };
      });

      // Kirim hasil function call kembali ke model
      const functionResult = await chat.sendMessage([
        {
          functionResponse: {
            name: functionResponses[0].name,
            response: functionResponses[0].response
          }
        }
      ]);

      const finalResponse = functionResult.response;
      const text: string = finalResponse.text();

      if (text) {
        console.log(`📱 ${cleanNumber} - Teks yang Dihasilkan (dengan function call):`, text);
        await sendWhatsAppMessage(text, senderNumber);
      }
    } else {
      // Respons normal tanpa function call
      const text: string = response.text();

      if (text) {
        console.log(`📱 ${cleanNumber} - Teks yang Dihasilkan:`, text);
        await sendWhatsAppMessage(text, senderNumber);
      } else {
        console.error("Masalah ini terkait dengan Keterbatasan Model dan Batas Tingkat API");
      }
    }

  } catch (error) {
    const cleanNumber = cleanPhoneNumber(senderNumber);
    console.error(`❌ ${cleanNumber} - Kesalahan dalam fungsi run:`, error);
    await sendWhatsAppMessage("Ups, terjadi kesalahan. Silakan coba lagi nanti.", senderNumber);
  }
}

async function sendWhatsAppMessage(text: string, toNumber: string): Promise<void> {
  try {
    await whatsappClient.sendMessage(toNumber, text);
  } catch (err) {
    console.error("Gagal mengirim pesan WhatsApp:");
    console.error("Detail kesalahan:", err);
  }
}

// Serve frontend
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// API endpoint untuk status bot
app.get('/api/status', (req, res) => {
  res.json({
    isReady: isWhatsAppReady,
    qrCode: currentQRCode,
    stats: {
      activeUsers: userChats.size,
      totalMessages: totalMessages,
      uptime: process.uptime(),
      lastClear: lastGlobalMemoryClearTime.toISOString()
    }
  });
});

// API endpoint untuk statistik
app.get('/api/stats', (req, res) => {
  res.json({
    activeUsers: userChats.size,
    totalMessages: totalMessages,
    uptime: process.uptime(),
    lastClear: lastGlobalMemoryClearTime.toISOString(),
    memory: process.memoryUsage()
  });
});

// API endpoint untuk refresh QR
app.post('/api/refresh-qr', (req, res) => {
  try {
    if (isWhatsAppReady) {
      res.json({ success: false, message: 'Bot sudah terhubung' });
    } else {
      // Restart WhatsApp client untuk mendapatkan QR baru
      whatsappClient.destroy().then(() => {
        setTimeout(() => {
          whatsappClient.initialize();
        }, 2000);
      });
      res.json({ success: true, message: 'QR refresh diminta' });
    }
  } catch (error) {
    res.json({ success: false, message: 'Error refresh QR' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    activeUsers: userChats.size,
    isWhatsAppReady: isWhatsAppReady
  });
});

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log('Client terhubung ke WebSocket');

  // Kirim status saat ini ke client baru
  socket.emit('status', {
    type: 'status',
    isReady: isWhatsAppReady,
    qrCode: currentQRCode,
    stats: {
      activeUsers: userChats.size,
      totalMessages: totalMessages,
      uptime: process.uptime(),
      lastClear: lastGlobalMemoryClearTime.toISOString()
    }
  });

  socket.on('disconnect', () => {
    console.log('Client terputus dari WebSocket');
  });
});

// Kirim update stats secara berkala
setInterval(() => {
  io.emit('stats', {
    type: 'stats',
    stats: {
      activeUsers: userChats.size,
      totalMessages: totalMessages,
      uptime: process.uptime(),
      lastClear: lastGlobalMemoryClearTime.toISOString()
    }
  });
}, 30000); // Setiap 30 detik

server.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Server berjalan di port ${port}!`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📱 Dashboard: http://localhost:${port}`);
  console.log(`⏰ Started at: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}`);
});
