# 🚀 Panduan Deploy WhatsApp Bot ke Railway

## 📋 Checklist Persiapan
- ✅ Frontend dashboard sudah dibuat
- ✅ WebSocket untuk real-time QR code
- ✅ API endpoints untuk monitoring
- ✅ Build berhasil tanpa error
- ✅ Railway configuration files ready

## 🔧 Langkah-langkah Deployment

### 1. Akses Railway
1. Buka browser dan kunjungi: **https://railway.app**
2. Klik **"Login"** atau **"Start a New Project"**
3. **Login dengan GitHub** (recommended)

### 2. Deploy Project
1. Klik **"New Project"**
2. Pilih **"Deploy from GitHub repo"**
3. Authorize Railway untuk akses GitHub
4. Pilih repository **`wa`** (WhatsApp bot project)
5. Klik **"Deploy Now"**

### 3. Set Environment Variables
Di dashboard Railway, klik tab **"Variables"** dan tambahkan:

```
API_KEY = your_gemini_api_key_here
NODE_ENV = production
```

**Cara mendapatkan API_KEY:**
- <PERSON>n<PERSON><PERSON>: https://makersuite.google.com/app/apikey
- Login dengan Google account
- Klik "Create API Key"
- Copy API key yang dihasilkan

### 4. Monitor Deployment
1. Klik tab **"Deployments"** untuk melihat progress
2. Tunggu hingga status **"Success"** (2-5 menit)
3. Klik **"View Logs"** jika ada error

### 5. Akses Dashboard
1. Klik tab **"Settings"**
2. Copy **URL** yang diberikan Railway
3. Buka URL di browser

## 🎯 Fitur Dashboard

### QR Code Scanner
- QR code ditampilkan secara real-time
- Auto-refresh ketika bot restart
- Status indicator (Connecting/Ready/Error)

### Live Monitoring
- User aktif real-time
- Total pesan
- Uptime server
- Memory usage

### Activity Logs
- Real-time logs dengan timestamp
- Color-coded log levels
- Auto-scroll untuk log terbaru

## 📱 Cara Menggunakan Bot

### Setup Awal
1. Buka dashboard Railway di browser
2. Tunggu QR code muncul
3. Scan QR code dengan WhatsApp
4. Tunggu status berubah menjadi "Ready"

### Mengirim Pesan ke Bot
Kirim pesan ke nomor WhatsApp yang sudah di-scan:

```
/halo
/jam berapa sekarang?
/status
/tanya tentang cuaca hari ini
/selesai (untuk clear memory)
```

## 🔍 Monitoring & Troubleshooting

### Health Check
- Dashboard: `https://your-app-url.up.railway.app/`
- Health: `https://your-app-url.up.railway.app/health`
- Stats API: `https://your-app-url.up.railway.app/api/stats`

### Common Issues
1. **QR Code tidak muncul**: Refresh halaman atau restart deployment
2. **Bot tidak respond**: Cek logs di Railway dashboard
3. **WebSocket error**: Pastikan browser support WebSocket

### Tips
- Jangan logout WhatsApp Web di browser lain
- QR code hanya perlu di-scan sekali
- Bot akan restart otomatis jika ada masalah
- Dashboard bisa diakses dari mobile

## 🌟 URL Penting

Setelah deploy, simpan URL berikut:
- **Dashboard**: `https://your-app-name.up.railway.app`
- **Railway Console**: `https://railway.app/project/your-project-id`

## 🎉 Selamat!

Bot WhatsApp AI dengan dashboard web Anda sudah siap digunakan!
