{"name": "whatsapp-bot-ai-gemini", "version": "1.0.0", "description": "WhatsApp Bot AI dengan Gemini untuk Railway deployment", "main": "dist/index.js", "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "railway:build": "npm run build", "railway:start": "npm run start"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/express": "^4.17.21", "@types/qrcode-terminal": "^0.12.2", "nodemon": "^3.1.4", "ts-node": "^10.9.2", "typescript": "^5.5.4"}, "dependencies": {"@google/generative-ai": "^0.17.1", "@types/qrcode": "^1.5.5", "@types/socket.io": "^3.0.1", "axios": "^1.7.5", "dotenv": "^16.4.5", "express": "^4.19.2", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "socket.io": "^4.8.1", "whatsapp-web.js": "^1.25.0"}}