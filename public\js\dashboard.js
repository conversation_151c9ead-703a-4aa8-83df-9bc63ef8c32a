class WhatsAppDashboard {
    constructor() {
        this.socket = null;
        this.logs = [];
        this.maxLogs = 50;
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.loadInitialData();
        this.startPeriodicUpdates();
        this.addLog('info', 'Dashboard dimulai');
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        try {
            this.socket = new WebSocket(wsUrl);
            
            this.socket.onopen = () => {
                this.addLog('info', 'WebSocket terhubung');
                this.updateConnectionStatus('connecting', 'Menghubungkan ke WhatsApp...');
            };

            this.socket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };

            this.socket.onclose = () => {
                this.addLog('warning', 'WebSocket terputus, mencoba reconnect...');
                setTimeout(() => this.connectWebSocket(), 5000);
            };

            this.socket.onerror = (error) => {
                this.addLog('error', 'WebSocket error: ' + error.message);
            };
        } catch (error) {
            this.addLog('error', 'Gagal membuat WebSocket connection');
            // Fallback ke polling jika WebSocket gagal
            this.startPolling();
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'qr':
                this.displayQRCode(data.qr);
                this.updateConnectionStatus('connecting', 'Scan QR Code dengan WhatsApp');
                break;
            case 'ready':
                this.updateConnectionStatus('ready', 'Bot siap dan terhubung!');
                this.hideQRCode();
                this.addLog('info', 'WhatsApp Bot siap digunakan');
                break;
            case 'disconnected':
                this.updateConnectionStatus('error', 'Terputus dari WhatsApp');
                this.addLog('error', 'Bot terputus dari WhatsApp');
                break;
            case 'message':
                this.addLog('info', `Pesan dari ${data.from}: ${data.message}`);
                break;
            case 'stats':
                this.updateStats(data.stats);
                break;
        }
    }

    async loadInitialData() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.qrCode) {
                this.displayQRCode(data.qrCode);
                this.updateConnectionStatus('connecting', 'Scan QR Code dengan WhatsApp');
            } else if (data.isReady) {
                this.updateConnectionStatus('ready', 'Bot siap dan terhubung!');
                this.hideQRCode();
            }
            
            this.updateStats(data.stats || {});
            this.addLog('info', 'Data awal dimuat');
        } catch (error) {
            this.addLog('error', 'Gagal memuat data awal: ' + error.message);
            this.updateConnectionStatus('error', 'Gagal terhubung ke server');
        }
    }

    displayQRCode(qrData) {
        const qrContainer = document.getElementById('qrContainer');
        qrContainer.innerHTML = `
            <img src="data:image/png;base64,${qrData}" alt="QR Code" class="qr-code">
            <p style="margin-top: 15px; color: #666;">Scan QR Code ini dengan WhatsApp Anda</p>
        `;
    }

    hideQRCode() {
        const qrContainer = document.getElementById('qrContainer');
        qrContainer.innerHTML = `
            <i class="fas fa-check-circle" style="font-size: 4rem; color: #28a745; margin-bottom: 15px;"></i>
            <p style="color: #28a745; font-weight: 500;">WhatsApp terhubung!</p>
            <p style="color: #666; margin-top: 10px;">Bot siap menerima pesan</p>
        `;
    }

    updateConnectionStatus(status, message) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.className = `status-indicator status-${status}`;
        
        let icon = 'fas fa-circle';
        if (status === 'ready') icon = 'fas fa-check-circle';
        if (status === 'error') icon = 'fas fa-exclamation-circle';
        
        statusElement.innerHTML = `<i class="${icon}"></i><span>${message}</span>`;
    }

    updateStats(stats) {
        document.getElementById('activeUsers').textContent = stats.activeUsers || 0;
        document.getElementById('totalMessages').textContent = stats.totalMessages || 0;
        
        if (stats.uptime) {
            const hours = Math.floor(stats.uptime / 3600);
            const minutes = Math.floor((stats.uptime % 3600) / 60);
            document.getElementById('uptime').textContent = `${hours}h ${minutes}m`;
        }
        
        if (stats.lastClear) {
            const date = new Date(stats.lastClear);
            document.getElementById('lastClear').textContent = date.toLocaleTimeString('id-ID');
        }
    }

    addLog(type, message) {
        const timestamp = new Date().toLocaleTimeString('id-ID');
        const log = { timestamp, type, message };
        
        this.logs.unshift(log);
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(0, this.maxLogs);
        }
        
        this.renderLogs();
    }

    renderLogs() {
        const logsContainer = document.getElementById('logsContainer');
        logsContainer.innerHTML = this.logs.map(log => `
            <div class="log-entry">
                <span class="log-timestamp">[${log.timestamp}]</span>
                <span class="log-${log.type}">${log.message}</span>
            </div>
        `).join('');
        
        logsContainer.scrollTop = 0;
    }

    async refreshQR() {
        try {
            this.addLog('info', 'Meminta QR Code baru...');
            const response = await fetch('/api/refresh-qr', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.addLog('info', 'QR Code baru diminta');
            } else {
                this.addLog('error', 'Gagal meminta QR Code baru');
            }
        } catch (error) {
            this.addLog('error', 'Error refresh QR: ' + error.message);
        }
    }

    startPeriodicUpdates() {
        // Update stats setiap 30 detik
        setInterval(async () => {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                this.updateStats(stats);
            } catch (error) {
                // Silent fail untuk periodic updates
            }
        }, 30000);
    }

    startPolling() {
        // Fallback polling jika WebSocket tidak tersedia
        setInterval(async () => {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.qrCode && !document.querySelector('.qr-code')) {
                    this.displayQRCode(data.qrCode);
                    this.updateConnectionStatus('connecting', 'Scan QR Code dengan WhatsApp');
                } else if (data.isReady) {
                    this.updateConnectionStatus('ready', 'Bot siap dan terhubung!');
                    this.hideQRCode();
                }
            } catch (error) {
                this.addLog('error', 'Polling error: ' + error.message);
            }
        }, 5000);
    }
}

// Global functions
function refreshQR() {
    if (window.dashboard) {
        window.dashboard.refreshQR();
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new WhatsAppDashboard();
});
